<?php

namespace Tests\Feature\Http\Controllers;

use App\Models\User;
use App\Services\ShopifyService;
use Tests\TestCase;

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @since  Jun, 2023
 *
 * @group subscriptions
 * @group integration
 *
 * @coversDefaultClass \App\Http\Controllers\SubscriptionsController
 */
class SubscriptionsControllerTest extends TestCase
{
	public static function dataProviderChargebee(): array
	{
		$webhookKey = '0k9UWOyxWy1xwN8iqzMW9xCerb1dJ50vaVLhjEAVjfM';

		return [
			'wrong key' => [
				'webhookData' => [
					'key' => 'wrong key',
				],
				'expected' => [
					'message' => 'Wrong key',
				],
			],
			'The user customer does not belong to the environment' => [
				'webhookData' => [
					'key' => $webhookKey,
					"content" => [
						"customer" => [
							'id' => "stage-ichive-user-000",
						],
					],
				],
				'expected' => [
					'message' => "SKIPPED: Chargebee User ID doesn't belong to environment: stage-ichive-user-000",
				],
			],
			'User ID Not found' => [
				'webhookData' => [
					'key' => $webhookKey,
					'content' => [
						"customer" => [
							'id' => "testing-ichive-user-",
						],
					]
				],
				'expected' => [
					'message' => "User doesn't exist"
				]
			],
			'User Not exist' => [
				'webhookData' => [
					'key' => $webhookKey,
					"content" => [
						"customer" => [
							'id' => "testing-ichive-user-999",
							"cf_user_id" => 9999,
						],
					],
				],
				'expected' => [
					'message' => "User doesn't exist",
				],
			],
		];
	}


	/**
	 * @covers ::chargebeeWebhook
	 * @return void
	 */
	public function test_chargebee_webhook(): void
	{
		$successResp = [
			'message' => 'OK!!!',
		];

		$user = User::create([
			'username' => "Testuser",
			'firstname' => "Test",
			'lastname' => "User",
			'email' => '<EMAIL>',
		]);

		$this->assertNull($user->chargebee);

		$webhookData = [
			'key' => config('services.chargebee.webhook_key'),
			"content" => [
				"subscription" => [
					"id" => "16BdcMTqFdE7HAup",
				],
				"customer" => [
					"id" => "testing-ichive-user-{$user->id}",
					"cf_user_id" => $user->id,
					'email' => $user->email,
				],
			],
		];

		$this->post(
			route('chargebee.webhook', $webhookData)
		)->assertExactJson($successResp)->assertOk();

		$user->refresh();

		$expected = $webhookData;
		unset($expected['key']);
		$this->assertEquals($expected, $user->chargebee->toArray()['data']);

		$shopifyUserApiData = app(ShopifyService::class)->searchForCustomer($user->email);
		$this->assertEquals($user->shopify->shopify_id, $shopifyUserApiData['id']);

		// Assert extract user id from customer ID if `cf_user_id` doesn't exist
		unset($webhookData['content']['customer']['cf_user_id']);
		$this->post(
			route('chargebee.webhook', $webhookData)
		)->assertExactJson($successResp)->assertOk();
	}

	/**
	 * @covers ::chargebeeWebhook
	 *
	 * @param array $webhookData
	 * @param array $expected
	 *
	 * @return void
	 *
	 * @dataProvider dataProviderChargebee
	 */
	public function test_chargebee_webhook_fails(array $webhookData, array $expected): void
	{
		$this->post(
			route('chargebee.webhook', $webhookData)
		)->assertExactJson($expected);
	}
}

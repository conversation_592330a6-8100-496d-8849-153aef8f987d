<?php

namespace Tests\Feature\Http\Controllers;

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Inertia\Testing\AssertableInertia;
use Tests\TestCase;

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @since  March, 2024
 *
 * @coversDefaultClass \App\Http\Controllers\SettingsController
 */
class SettingsControllerTest extends TestCase
{
	/**
	 * @covers ::profile
	 */
	public function testProfile(): void
	{
		$user = User::factory()->create();

		$this->actingAs($user);

		$this->get(route('settings.profile'))
			->assertInertia(fn(AssertableInertia $page) => $page
				->component('User/Settings/Profile')
				->where('profile.id', $user->id));
	}

	/**
	 * @covers ::updateProfile
	 */
	public function testUpdateProfile(): void
	{
		$user = User::factory()->state([
			'email' => '<EMAIL>',
		])->create();

		$updateData = [
			'firstname' => 'mychive',
			'email' => '<EMAIL>',
			'dob' => '1990-02-11'
		];

		$this->actingAs($user);

		$this->post(route('settings.profile.update'), $updateData)
			->assertRedirect()->assertSessionHas('success');

		$user->refresh();

		$this->assertEquals($updateData, [
			'firstname' => $user->firstname,
			'email' => $user->email,
			'dob' => $user->data->dob->format('Y-m-d'),
		]);
	}

	/**
	 * @covers ::social
	 */
	public function testSocial(): void
	{
		$user = User::factory()->create();

		$this->actingAs($user);

		$this->get(route('settings.social'))
			->assertInertia(fn(AssertableInertia $page) => $page
				->component('User/Settings/Social')
				->where('socialLinks', [
					'is_verified_chivette' => $user->is_verified_chivette,
					'website' => $user->data->website,
					'facebook' => $user->data->facebook,
					'instagram' => $user->data->instagram,
					'tiktok' => $user->data->tiktok,
					'twitter' => $user->data->twitter,
				]));
	}

	/**
	 * @covers ::membership
	 */
	public function testMembership(): void
	{
		$user = User::factory()->create();

		$this->actingAs($user);

		$this->get(route('settings.membership'))
			->assertInertia(fn(AssertableInertia $page) => $page
				->component('User/Settings/Membership')
				->has('chargebeePortalUrl'));
	}

	/**
	 * @covers ::account
	 */
	public function testAccount(): void
	{
		$user = User::factory()->create();

		$this->actingAs($user);

		$this->get(route('settings.account'))
			->assertInertia(fn(AssertableInertia $page) => $page
				->component('User/Settings/Account')
				->where('username', $user->username)
				->where('private', $user->data->private)
				->where('allowNsfwContent', $user->data->allow_nsfw_content)
				->where('hasPassword', (boolean)$user->password));
	}

	/**
	 * @covers ::updateOptions
	 */
	public function testUpdateOptions(): void
	{
		$user = User::factory()->create();

		$this->actingAs($user);

		$updateData = [
			'allow_nsfw_content' => true
		];

		$this->post(
			route(
				'settings.options.update',
			),
			$updateData
		)->assertRedirect()->assertSessionHas('success');

		// Assert data should be updated
		$user->refresh();
		$this->assertEquals($updateData, [
			'allow_nsfw_content' => $user->data->allow_nsfw_content
		]);
	}

	/**
	 * @covers ::emailPreferences
	 */
	public function testEmailPreferences(): void
	{
		$user = User::factory()->create();

		$this->actingAs($user);

		$this->get(route('settings.email.preferences'))
			->assertInertia(fn(AssertableInertia $page) => $page
				->component('User/Settings/EmailPreferences')
				->has('allUserSubscriptions'));
	}

	/**
	 * @covers ::deleteAccount
	 */
	public function testDeleteAccount(): void
	{
		$user = User::factory()->create();

		$this->actingAs($user);

		$this->post(route('settings.account.delete'))
			->assertOk();

		$this->assertFalse(Auth::check());
		$this->assertNull(User::whereId($user->id)->first());
	}
}

<?php

namespace Tests\Unit;

use App\Services\ShopifyService;
use App\Repositories\UserRepository;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;
use Mockery;

class ShopifyServiceEmailCaseTest extends TestCase
{
    protected ShopifyService $shopifyService;
    protected $userRepositoryMock;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->userRepositoryMock = Mockery::mock(UserRepository::class);
        $this->shopifyService = new ShopifyService($this->userRepositoryMock);
    }

    /** @test */
    public function it_searches_for_customer_with_lowercase_email()
    {
        // Mock the HTTP response
        Http::fake([
            '*/admin/customers/search.json*' => Http::response([
                'customers' => [
                    [
                        'id' => 123,
                        'email' => '<EMAIL>',
                        'first_name' => 'John',
                        'last_name' => 'Doe'
                    ]
                ]
            ])
        ]);

        $result = $this->shopifyService->searchForCustomer('<EMAIL>');

        $this->assertNotEmpty($result);
        $this->assertEquals('<EMAIL>', $result['email']);
        $this->assertEquals(123, $result['id']);
    }

    /** @test */
    public function it_searches_for_customer_with_uppercase_email()
    {
        // Mock the HTTP response - Shopify returns lowercase email
        Http::fake([
            '*/admin/customers/search.json*' => Http::response([
                'customers' => [
                    [
                        'id' => 123,
                        'email' => '<EMAIL>',
                        'first_name' => 'John',
                        'last_name' => 'Doe'
                    ]
                ]
            ])
        ]);

        // Search with uppercase email
        $result = $this->shopifyService->searchForCustomer('<EMAIL>');

        $this->assertNotEmpty($result);
        $this->assertEquals('<EMAIL>', $result['email']);
        $this->assertEquals(123, $result['id']);

        // Verify the request was made with lowercase email
        Http::assertSent(function ($request) {
            return str_contains($request->url(), 'query=email:<EMAIL>');
        });
    }

    /** @test */
    public function it_searches_for_customer_with_mixed_case_email()
    {
        // Mock the HTTP response
        Http::fake([
            '*/admin/customers/search.json*' => Http::response([
                'customers' => [
                    [
                        'id' => 456,
                        'email' => '<EMAIL>',
                        'first_name' => 'Jane',
                        'last_name' => 'Doe'
                    ]
                ]
            ])
        ]);

        // Search with mixed case email
        $result = $this->shopifyService->searchForCustomer('<EMAIL>');

        $this->assertNotEmpty($result);
        $this->assertEquals('<EMAIL>', $result['email']);
        $this->assertEquals(456, $result['id']);

        // Verify the request was made with lowercase email
        Http::assertSent(function ($request) {
            return str_contains($request->url(), 'query=email:<EMAIL>');
        });
    }

    /** @test */
    public function it_returns_empty_array_when_customer_not_found()
    {
        // Mock empty response
        Http::fake([
            '*/admin/customers/search.json*' => Http::response([
                'customers' => []
            ])
        ]);

        $result = $this->shopifyService->searchForCustomer('<EMAIL>');

        $this->assertEmpty($result);
        $this->assertEquals([], $result);
    }

    /** @test */
    public function it_handles_multiple_customers_and_finds_exact_match()
    {
        // Mock response with multiple customers
        Http::fake([
            '*/admin/customers/search.json*' => Http::response([
                'customers' => [
                    [
                        'id' => 111,
                        'email' => '<EMAIL>',
                        'first_name' => 'Other',
                        'last_name' => 'User'
                    ],
                    [
                        'id' => 222,
                        'email' => '<EMAIL>',
                        'first_name' => 'Target',
                        'last_name' => 'User'
                    ]
                ]
            ])
        ]);

        $result = $this->shopifyService->searchForCustomer('<EMAIL>');

        $this->assertNotEmpty($result);
        $this->assertEquals('<EMAIL>', $result['email']);
        $this->assertEquals(222, $result['id']);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}

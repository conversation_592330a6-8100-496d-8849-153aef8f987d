<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:noNamespaceSchemaLocation="./vendor/phpunit/phpunit/phpunit.xsd"
		 bootstrap="vendor/autoload.php"
		 colors="true"
>
	<testsuites>
		<testsuite name="Unit">
			<directory suffix="Test.php">./tests/Unit</directory>
		</testsuite>
		<testsuite name="Feature">
			<directory suffix="Test.php">./tests/Feature</directory>
		</testsuite>
	</testsuites>
	<php>
		<env name="APP_ENV" value="testing"/>
		<env name="BCRYPT_ROUNDS" value="4"/>
		<env name="CACHE_DRIVER" value="array"/>
		<env name="DB_DATABASE" value="testing"/>
		<env name="DB_CONNECTION" value="mysql"/>
		<env name="MAIL_MAILER" value="array"/>
		<env name="QUEUE_CONNECTION" value="sync"/>
		<env name="SESSION_DRIVER" value="array"/>
		<env name="TELESCOPE_ENABLED" value="false"/>
		<env name="LARAVEL_DISABLE_LOGGING" value="true"/>
	</php>
</phpunit>

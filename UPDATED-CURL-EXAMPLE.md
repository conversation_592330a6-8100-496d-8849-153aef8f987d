# Updated CURL Example for searchForCustomer Method

After fixing the email case sensitivity issue, here's the updated CURL request example:

## Fixed Method Behavior

The `searchForCustomer` method now:
1. Converts the input email to lowercase before sending to Shopify API
2. Performs case-insensitive comparison when checking results
3. Maintains consistency with the `createCustomer` method

## Example CURL Request

```bash
# The method will automatically convert the email to lowercase
# So even if you search for "<EMAIL>", it will query "<EMAIL>"

curl -X GET \
  "https://thechivery-staging.myshopify.com/admin/customers/search.json?query=email:<EMAIL>" \
  -H "Content-Type: application/json" \
  -u "8d1ba4eaa1019cc7e322dcb12ec2f69c:e20d55fd453e59cff8cfcc7a533055fb" \
  --connect-timeout 30
```

## Test Cases

### 1. Uppercase Email Input
```bash
# Input: "<EMAIL>" 
# Actual query sent: "email:<EMAIL>"
curl -X GET \
  "https://thechivery-staging.myshopify.com/admin/customers/search.json?query=email:<EMAIL>" \
  -H "Content-Type: application/json" \
  -u "8d1ba4eaa1019cc7e322dcb12ec2f69c:e20d55fd453e59cff8cfcc7a533055fb" \
  --connect-timeout 30
```

### 2. Mixed Case Email Input
```bash
# Input: "<EMAIL>"
# Actual query sent: "email:<EMAIL>"
curl -X GET \
  "https://thechivery-staging.myshopify.com/admin/customers/search.json?query=email:<EMAIL>" \
  -H "Content-Type: application/json" \
  -u "8d1ba4eaa1019cc7e322dcb12ec2f69c:e20d55fd453e59cff8cfcc7a533055fb" \
  --connect-timeout 30
```

### 3. Lowercase Email Input (unchanged behavior)
```bash
# Input: "<EMAIL>"
# Actual query sent: "email:<EMAIL>"
curl -X GET \
  "https://thechivery-staging.myshopify.com/admin/customers/search.json?query=email:<EMAIL>" \
  -H "Content-Type: application/json" \
  -u "8d1ba4eaa1019cc7e322dcb12ec2f69c:e20d55fd453e59cff8cfcc7a533055fb" \
  --connect-timeout 30
```

## Key Changes Made

1. **Email Normalization**: Input email is converted to lowercase before API call
2. **Case-Insensitive Comparison**: Customer email comparison is now case-insensitive
3. **Consistency**: Matches the behavior of `createCustomer` method
4. **Backward Compatibility**: Existing lowercase email searches continue to work unchanged

## Expected Response

The API will return the same JSON structure as before, but now it will successfully find customers regardless of the case of the input email:

```json
{
  "customers": [
    {
      "id": 123456789,
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "created_at": "2023-01-01T00:00:00-00:00",
      "updated_at": "2023-01-01T00:00:00-00:00",
      // ... other customer fields
    }
  ]
}
```

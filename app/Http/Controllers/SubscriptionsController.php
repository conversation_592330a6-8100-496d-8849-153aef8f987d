<?php

namespace App\Http\Controllers;

use App\Events\UserMembershipCreated;
use App\Events\UserMembershipDeleted;
use App\Models\User;
use App\Models\UserSubscription;
use App\Repositories\UserRepository;
use App\Services\ChargebeeService;
use App\Services\ShopifyService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;

class SubscriptionsController extends Controller
{

	private const AD_FREE_SHOPIFY_TAG = 'chive-subscription';

	public function __construct(
		protected UserRepository $userRepository,
		protected ShopifyService $shopifyService,
		protected ChargebeeService $chargebeeService,
	) {
		$this->middleware('auth')->only('setMychiveSubscription', 'renewEarlySubscription');
	}

	public function setMychiveSubscription(): Response
	{
		UserSubscription::updateOrCreate([
			'user_id' => auth()->user()->id,
		], [
			'mychive' => 1,
		]);

		return response(trans('user.subscription-success'));
	}

	public function checkoutPortal(string $priceId): Response
	{
		$checkoutData = $this->chargebeeService->getCheckoutHostedPortalByPriceId(auth()->user(), $priceId);
		if (!$checkoutData) {
			return response('Error getting checkout portal', 400);
		}
		return response($checkoutData);
	}

	/**
	 * The webhook endpoint for update user's subscription status from chargebee.
	 */
	public function chargebeeWebhook(Request $request): Response
	{
		if ($request->get('key') !== config('services.chargebee.webhook_key')) {
			return response(['message' => 'Wrong key'], 401);
		}

		$webhookData = $request->except('key');

		// Try to extract the user ID from customer ID
		$userId = $webhookData['content']['customer']['cf_user_id'] ?? '';

		$webhookCustomerId = $webhookData['content']['customer']['id'] ?? '';

		if (!$this->userChargebeeIdBelongToWebhookEnv($webhookCustomerId)) {
			return response([
				'message' => "SKIPPED: Chargebee User ID doesn't belong to environment: $webhookCustomerId",
			]);
		}

		if (!$userId && $webhookCustomerId) {
			$userId = explode('user-', $webhookCustomerId)[1] ?? '';
		}

		if (!empty($userId)) {
			$user = User::whereId($userId)->first();
		} else {
			$user = User::whereEmail($webhookData['content']['customer']['email'] ?? '')->first();
		}

		if (!$user) {
			Log::info("User doesn't exist userId: " . $userId);
			return response(['message' => "User doesn't exist"]);
		}

		$this->chargebeeService->syncSubscriptionData($user);

		if (!in_array(strtolower($user->email), config('subscriptions.skip_emails'), true) && $user->email) {
			try {
				$user = $this->createUpdateShopify($user);
			} catch (\RuntimeException $e) {
				return response(['message' => "Error creating/updating Shopify account: " . $e->getMessage()], 400);
			}

			$shopifyTags = [self::AD_FREE_SHOPIFY_TAG];
			if ($user->subscription->thechive) {
				$this->shopifyService->addTags($user->shopify, $shopifyTags);
				event(new UserMembershipCreated($user));
			} else {
				$this->shopifyService->removeTags($user->shopify, $shopifyTags);
				event(new UserMembershipDeleted($user));
			}
		} else {
			Log::info("Skip emailing or email doesn't exist for user: " . $user->email);
		}

		$this->userRepository->updateChargebeeData($user, $webhookData);

		return response(['message' => 'OK!!!']);
	}

	private function createUpdateShopify(User $user): User
	{
		if (null === $user->shopify) {
			$tries = 3;
			// attempt 3 times to create an account
			for ($i = 1; $i <= $tries; $i++) {
				$updatedUser = $this->shopifyService->setupCustomer($user);

				// if there were no errors updating the user's shopify, just break the loop
				if ($updatedUser && null !== $updatedUser->shopify) {
					$user = $updatedUser;
					break;
				}

				if ($i >= $tries) {
					throw new \RuntimeException('Error creating customer in Shopify');
				}

				// sleep for a second before trying to create the account
				// again
				sleep(1);
			}
		}

		return $user;
	}

	public function renewEarlySubscription(Request $request): Response
	{
		$validated = $request->validate([
			'subscriptionId' => 'required',
		]);

		$resp = $this->chargebeeService->chargeFutureRenewals($validated['subscriptionId']);

		return response($resp, is_null($resp) ? 500 : 200);
	}

	/**
	 * Chargebee webhook for the test environment is calling multiple environments at the same time
	 * we have to be sure to update the user info that match with environment
	 * production is the only environment set with one single hook
	 *
	 * @param string $webhookCustomerId
	 * @return bool
	 */
	private function userChargebeeIdBelongToWebhookEnv(string $webhookCustomerId): bool
	{
		if (App::isProduction() || !str_contains($webhookCustomerId, ChargebeeService::PREFIX_ID)) {
			return true;
		}

		$userEnv = trim(explode(ChargebeeService::PREFIX_ID, $webhookCustomerId)[0], '-');

		return $userEnv === App::environment();
	}
}

<?php

namespace App\Providers;

use App\Repositories\UserRepository;
use App\Services\ShopifyService;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\ServiceProvider;

class ShopifyServiceProvider extends ServiceProvider
{
	public function register(): void
	{
		$this->app->bind(ShopifyService::class, function ($app) {
			return new ShopifyService(
				$app[UserRepository::class]
			);
		});
	}

	public function boot(): void
	{
		Http::macro('shopify', function () {
			return Http::withBasicAuth(
				config('services.shopify.api_username'),
				config('services.shopify.api_password')
			)->baseUrl(
				config('services.shopify.api_url')
			)->connectTimeout(30);
		});
	}
}

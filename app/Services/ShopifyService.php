<?php namespace App\Services;

use App\Models\User;
use App\Models\UserShopifyData;
use App\Repositories\UserRepository;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ShopifyService
{
	public function __construct(
		protected UserRepository $userRepository
	) {
	}

	public function setupCustomer(User $user): ?User
	{
		if (!$user->email) {
			return null;
		}

		$customer = $this->searchForCustomer($user->email);

		if (!$customer) {
			$this->createCustomer($user);
		} else {
			$this->userRepository->updateShopifyData($user, $customer);
		}

		return $user;
	}

	/**
	 * Searched for shopify customer based off email address. Returns false if not found, or the entire Shopify
	 * object if it is found
	 */
	public function searchForCustomer(string $email): array
	{
		$data = Http::shopify()
			->withOptions(['http_errors' => true])
			->get('admin/customers/search.json', [
				'query' => 'email:' . $email
			])->json();

		if (isset($data['customers']) && count($data['customers'])) {
			foreach ($data['customers'] as $customer) {
				if ($customer['email'] === $email) {
					return $customer;
				}
			}
		}

		return [];
	}

	/**
	 * Add tags to a customer.
	 */
	public function addTags(UserShopifyData $userShopifyData, array $tags = []): string
	{
		$currentUserTags = array_filter(explode(', ', $userShopifyData->tags ?? ''));

		$userTags = implode(
			', ',
			array_unique(
				array_merge($currentUserTags, $tags),
				SORT_REGULAR
			)
		);

		$userShopifyData->tags = $userTags;

		$this->updateTags($userShopifyData, $userTags);

		return $userTags;
	}

	/**
	 * Remove tags from a customer.
	 */
	public function removeTags(UserShopifyData $userShopifyData, array $tags = []): string
	{
		$currentUserTags = array_filter(explode(', ', $userShopifyData->tags ?? ''));
		$userTags = implode(', ', array_diff($currentUserTags, $tags));

		$this->updateTags($userShopifyData, $userTags);

		return $userTags;
	}

	/**
	 * Create a customer in Shopify.
	 */
	public function createCustomer(User $user): array
	{
		$data = [
			'customer' => [
				'first_name' => $user->firstname,
				'last_name' => $user->lastname,
				'email' => strtolower($user->email),
				'verified_email' => (isset($user->confirmed_at)),
			]
		];

		$res = Http::shopify()->post('admin/customers.json', $data)->json();
		
		if (!isset($res['errors'])) {
			$this->userRepository->updateShopifyData($user, $res['customer']);
			return $res['customer'];
		}

		Log::error('Shopify: Error creating customer {email}', ['email' => $user->email]);
		Log::error(print_r($res, true));

		return [];
	}

	private function updateTags(UserShopifyData $userShopifyData, string $tags): void
	{
		$url = 'admin/customers/' . $userShopifyData->shopify_id . '.json';

		Http::shopify()->put($url, [
			'customer' => [
				'tags' => $tags,
			],
		])->json();
	}
}

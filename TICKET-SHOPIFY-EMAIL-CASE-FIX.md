# Ticket: Fix ShopifyService searchForCustomer Email Case Sensitivity Issue

## Issue Description
The `searchForCustomer` method in `ShopifyService` is returning empty results when searching for customers with uppercase email addresses. This creates inconsistency with the `createCustomer` method which normalizes emails to lowercase.

## Problem Details
1. **Location**: `app/Services/ShopifyService.php` - `searchForCustomer()` method (lines 37-54)
2. **Root Cause**: The method performs a strict comparison (`===`) between the search email and customer email without normalizing case
3. **Impact**: 
   - Users with uppercase emails in their accounts cannot be found in Shopify searches
   - This can lead to duplicate customer creation attempts
   - Inconsistent behavior between search and create operations

## Current Behavior
```php
// In searchForCustomer method (line 47)
if ($customer['email'] === $email) {
    return $customer;
}
```

## Expected Behavior
- Email searches should be case-insensitive
- Consistent email normalization across all Shopify operations
- Both search query and comparison should use lowercase emails

## Consistency Issue
- `createCustomer()` method already normalizes emails: `'email' => strtolower($user->email)` (line 100)
- `searchForCustomer()` should follow the same pattern for consistency

## Solution Required
1. Normalize the input email to lowercase before sending to Shopify API
2. Normalize the comparison email to lowercase when checking results
3. Ensure consistency with existing `createCustomer` method behavior

## Priority
**Medium** - This affects user experience and can cause data inconsistencies

## Acceptance Criteria
- [ ] `searchForCustomer` method handles uppercase emails correctly
- [ ] Email comparison is case-insensitive
- [ ] Consistent email normalization across all Shopify service methods
- [ ] No breaking changes to existing functionality
- [ ] Unit tests added to verify case-insensitive email search

## Files to Modify
- `app/Services/ShopifyService.php`

## Testing Requirements
- Test with uppercase email addresses
- Test with mixed case email addresses  
- Test with lowercase email addresses (existing functionality)
- Verify integration with `setupCustomer` method workflow
